#!/bin/bash
# Build and push backend Docker images to dev environment using Terraform outputs
# This script automatically retrieves project info from Terraform state

set -euo pipefail

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
print_error() {
    echo -e "${RED}❌ Error: $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_step() {
    echo -e "${BLUE}🚀 $1${NC}"
}

# Change to workspace directory
if [ -n "${BUILD_WORKSPACE_DIRECTORY:-}" ]; then
    cd "$BUILD_WORKSPACE_DIRECTORY"
elif [ -n "${TEST_WORKSPACE:-}" ]; then
    cd "$TEST_WORKSPACE"
else
    # Find the workspace root by looking for WORKSPACE file
    CURRENT_DIR="$PWD"
    while [ "$CURRENT_DIR" != "/" ]; do
        if [ -f "$CURRENT_DIR/WORKSPACE" ]; then
            cd "$CURRENT_DIR"
            break
        fi
        CURRENT_DIR="$(dirname "$CURRENT_DIR")"
    done
fi

echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "🐳 BUILD AND PUSH BACKEND IMAGES TO DEV ENVIRONMENT"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""
print_info "Working directory: $(pwd)"
echo ""

# Step 1: Get Terraform outputs
print_step "Step 1: Retrieving Terraform configuration"
echo ""

cd terraform/environments/dev
if [ ! -f "main.tf" ]; then
    print_error "Terraform configuration not found in terraform/environments/dev"
    exit 1
fi

# Check if terraform is initialized
if [ ! -d ".terraform" ]; then
    print_info "Initializing Terraform..."
    terraform init
fi

# Get project info from Terraform
print_info "Getting project configuration from Terraform..."
PROJECT_ID=$(terraform output -json dev_access_info 2>/dev/null | jq -r '.project_id' || echo "")
REGISTRY_URL=$(terraform output -raw registry_url 2>/dev/null || echo "")

if [ -z "$PROJECT_ID" ] || [ -z "$REGISTRY_URL" ]; then
    print_error "Could not retrieve project configuration from Terraform"
    print_info "Please ensure Terraform is applied:"
    echo "  cd terraform/environments/dev"
    echo "  terraform apply"
    exit 1
fi

print_success "Project configuration retrieved"
echo "  🏗️  Project ID: $PROJECT_ID"
echo "  📦 Registry URL: $REGISTRY_URL"
echo ""

# Return to workspace root
cd ../../..

# Step 2: Build Docker images
print_step "Step 2: Building Docker images"
echo ""

print_info "Building Go service images for linux/amd64..."
if bazel build --platforms=@rules_go//go/toolchain:linux_amd64 //services/gateway:gateway_image //services/auth:auth_service_image //apps/crm/backend:crm_backend_image; then
    print_success "Go service images built successfully"
else
    print_error "Failed to build Go service images"
    exit 1
fi

print_info "Building Project Hub backend Docker image..."
cd apps/project-hub/backend
if docker build -t project-hub-backend:latest .; then
    print_success "Project Hub backend image built successfully"
    cd ../../..
else
    print_error "Failed to build Project Hub backend image"
    cd ../../..
    exit 1
fi
echo ""

# Step 3: Load images to Docker daemon
print_step "Step 3: Loading images to Docker daemon"
echo ""

print_info "Converting OCI images to Docker format..."

# Function to convert OCI layout to Docker tarball and load
load_oci_image() {
    local oci_path="$1"
    local tag="$2"
    local temp_tar="/tmp/$(basename $oci_path).tar"
    
    print_info "Converting $oci_path to Docker format..."
    
    # Create a tarball from the OCI layout
    if tar -C "$oci_path" -cf "$temp_tar" .; then
        print_info "Loading $tag into Docker daemon..."
        if docker load < "$temp_tar"; then
            # Tag the loaded image with our desired name
            local loaded_id=$(docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.ID}}" | grep "<none>" | head -1 | awk '{print $3}')
            if [ -n "$loaded_id" ]; then
                docker tag "$loaded_id" "$tag"
                print_success "Successfully loaded and tagged $tag"
            else
                print_warning "Image loaded but couldn't auto-tag, will try alternative method"
                return 1
            fi
        else
            print_error "Failed to load image into Docker daemon"
            return 1
        fi
    else
        print_error "Failed to create tarball from OCI layout"
        return 1
    fi
    
    # Clean up temp file
    rm -f "$temp_tar"
}

# Try to load each image, fallback to skipping if OCI format is incompatible
print_info "Loading gateway image..."
if load_oci_image "bazel-bin/platform/gateway/gateway_image" "gateway_image:latest"; then
    print_success "Gateway image loaded"
else
    print_warning "Could not load gateway image, will try direct push to registry"
    SKIP_DOCKER_LOAD="true"
fi

if [ "$SKIP_DOCKER_LOAD" != "true" ]; then
    print_info "Loading auth service image..."
    if load_oci_image "bazel-bin/platform/auth/auth_service_image" "auth_service_image:latest"; then
        print_success "Auth service image loaded"
    else
        print_warning "Could not load auth image, will try direct push to registry"
        SKIP_DOCKER_LOAD="true"
    fi
fi

if [ "$SKIP_DOCKER_LOAD" != "true" ]; then
    print_info "Loading CRM backend image..."
    if load_oci_image "bazel-bin/platform/crm_backend/crm_backend_image" "crm_backend_image:latest"; then
        print_success "CRM backend image loaded"
    else
        print_warning "Could not load CRM backend image, will try direct push to registry"
        SKIP_DOCKER_LOAD="true"
    fi
fi

if [ "$SKIP_DOCKER_LOAD" == "true" ]; then
    print_warning "Falling back to direct registry push using crane"
else
    print_success "All images loaded to Docker daemon"
fi
echo ""

# Step 4: Authenticate with registry
print_step "Step 4: Authenticating with Google Container Registry"
echo ""

print_info "Configuring Docker authentication..."

# Determine registry hostname from registry URL
REGISTRY_HOSTNAME=$(echo "$REGISTRY_URL" | cut -d'/' -f1)
print_info "Registry hostname: $REGISTRY_HOSTNAME"

# Configure authentication for the specific registry
if gcloud auth configure-docker "$REGISTRY_HOSTNAME" --quiet; then
    print_success "Docker authentication configured for $REGISTRY_HOSTNAME"
else
    print_error "Failed to configure Docker authentication for $REGISTRY_HOSTNAME"
    exit 1
fi
echo ""

# Step 5: Tag and push images
print_step "Step 5: Tagging and pushing images to registry"
echo ""

print_info "Tagging and pushing all backend services..."

# Get crane command if we need it for direct push
CRANE_CMD=""
if [ "$SKIP_DOCKER_LOAD" == "true" ]; then
    if command -v crane &> /dev/null; then
        CRANE_CMD="crane"
    elif [ -f "$HOME/go/bin/crane" ]; then
        CRANE_CMD="$HOME/go/bin/crane"
    elif [ -f "$(go env GOPATH)/bin/crane" ]; then
        CRANE_CMD="$(go env GOPATH)/bin/crane"
    fi
    print_info "Will use crane for direct OCI push: $CRANE_CMD"
fi

if [ "$SKIP_DOCKER_LOAD" == "true" ]; then
    # Direct push using crane
    print_info "Pushing images directly from OCI format using crane..."
    
    print_info "Pushing gateway image..."
    if $CRANE_CMD push "bazel-bin/services/gateway/gateway_image" "$REGISTRY_URL/gateway:latest"; then
        print_success "Pushed gateway image"
    else
        print_error "Failed to push gateway image"
        exit 1
    fi
    
    print_info "Pushing auth service image..."
    if $CRANE_CMD push "bazel-bin/services/auth/auth_service_image" "$REGISTRY_URL/auth:latest"; then
        print_success "Pushed auth service image"
    else
        print_error "Failed to push auth service image"
        exit 1
    fi
    
    print_info "Pushing CRM backend image..."
    if $CRANE_CMD push "bazel-bin/apps/crm/backend/crm_backend_image" "$REGISTRY_URL/crm-backend:latest"; then
        print_success "Pushed CRM backend image"
    else
        print_error "Failed to push CRM backend image"
        exit 1
    fi
    
    print_info "Pushing Project Hub backend image..."
    if docker tag project-hub-backend:latest "$REGISTRY_URL/project-hub-backend:latest" && docker push "$REGISTRY_URL/project-hub-backend:latest"; then
        print_success "Pushed Project Hub backend image"
    else
        print_error "Failed to push Project Hub backend image"
        exit 1
    fi
    
else
    # Traditional Docker daemon approach
    print_info "Using Docker daemon to tag and push images..."
    
    # Define image mappings
    declare -A IMAGES=(
        ["gateway_image:latest"]="$REGISTRY_URL/gateway:latest"
        ["auth_service_image:latest"]="$REGISTRY_URL/auth:latest"
        ["crm_backend_image:latest"]="$REGISTRY_URL/crm-backend:latest"
        ["project-hub-backend:latest"]="$REGISTRY_URL/project-hub-backend:latest"
    )

    # Tag all images
    for local_image in "${!IMAGES[@]}"; do
        remote_image="${IMAGES[$local_image]}"
        print_info "Tagging $local_image -> $remote_image"
        docker tag "$local_image" "$remote_image"
    done

    # Push all images
    for local_image in "${!IMAGES[@]}"; do
        remote_image="${IMAGES[$local_image]}"
        print_info "Pushing $remote_image..."
        if docker push "$remote_image"; then
            print_success "Pushed $remote_image"
        else
            print_error "Failed to push $remote_image"
            exit 1
        fi
    done
fi

echo ""
print_success "All backend images pushed successfully!"
echo ""

# Step 6: Display summary
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
print_success "BACKEND IMAGES DEPLOYMENT COMPLETED!"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""
echo "🐳 Pushed Images:"
if [ "$SKIP_DOCKER_LOAD" == "true" ]; then
    echo "  📦 $REGISTRY_URL/gateway:latest"
    echo "  📦 $REGISTRY_URL/auth:latest"
    echo "  📦 $REGISTRY_URL/crm-backend:latest"
    echo "  📦 $REGISTRY_URL/project-hub-backend:latest"
else
    for local_image in "${!IMAGES[@]}"; do
        remote_image="${IMAGES[$local_image]}"
        echo "  📦 $remote_image"
    done
fi
echo ""
echo "🎯 Next Steps:"
echo "  1. Update your compute instances to use the new images"
echo "  2. Restart services on your GCP instances:"
echo "     gcloud compute ssh \$(terraform output -raw instance_name) --zone=australia-southeast1-a \\"
echo "       --command='sudo systemctl restart platform-services'"
echo ""
echo "📊 Deployment Info:"
echo "  • Project: $PROJECT_ID"
echo "  • Registry: $REGISTRY_URL"
echo "  • Environment: dev"
echo ""
print_success "Ready for deployment! 🚀"