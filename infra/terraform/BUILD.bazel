load("@bazel_tools//tools/build_defs/pkg:pkg.bzl", "pkg_tar")

# Terraform module validation and linting
sh_test(
    name = "terraform_validate",
    srcs = ["scripts/validate.sh"],
    data = glob(
        [
            "**/*.tf",
            "**/*.tfvars",
            "**/*.terraform.lock.hcl",
        ],
        allow_empty = True,
    ),
    tags = ["terraform"],
)

sh_test(
    name = "terraform_lint",
    srcs = ["scripts/lint.sh"],
    data = glob(
        [
            "**/*.tf",
            "**/*.tfvars",
        ],
        allow_empty = True,
    ),
    tags = [
        "lint",
        "terraform",
    ],
)

sh_test(
    name = "terraform_format_check",
    srcs = ["scripts/format_check.sh"],
    data = glob(
        [
            "**/*.tf",
            "**/*.tfvars",
        ],
        allow_empty = True,
    ),
    tags = [
        "format",
        "terraform",
    ],
)

# Security scanning for Terraform
sh_test(
    name = "terraform_security_scan",
    srcs = ["scripts/security_scan.sh"],
    data = glob(
        [
            "**/*.tf",
            "**/*.tfvars",
        ],
        allow_empty = True,
    ),
    tags = [
        "security",
        "terraform",
    ],
)

# Package Terraform configurations
pkg_tar(
    name = "terraform_package",
    srcs = glob(
        [
            "**/*.tf",
            "**/*.tfvars.example",
            "**/README.md",
        ],
        allow_empty = True,
    ),
    package_dir = "terraform",
    tags = ["terraform"],
)

# Terraform documentation generation
genrule(
    name = "terraform_docs",
    srcs = glob(
        [
            "**/*.tf",
            "**/README.md",
        ],
        allow_empty = True,
    ),
    outs = ["docs/terraform_generated_docs.md"],
    cmd = "$(location scripts/generate_docs.sh) $(SRCS) > $@",
    tags = [
        "docs",
        "terraform",
    ],
    tools = ["scripts/generate_docs.sh"],
)

# Build and deploy web application to dev environment
sh_binary(
    name = "deploy_web_dev",
    srcs = ["scripts/build_and_deploy_web_dev.sh"],
    data = [
        "//apps/crm/web:build_dist_complete",
        "//apps/crm/web/api-client:generate_client",
    ],
    tags = [
        "deploy",
        "dev",
        "terraform",
    ],
)

# Build and deploy web application to dev environment with database migration
sh_binary(
    name = "deploy_web_dev_with_migration",
    srcs = ["scripts/build_and_deploy_web_dev.sh"],
    args = ["--with-migration"],
    data = [
        "//apps/crm/web:build_dist_complete",
        "//infra/database:migrate_gcp_dev_simple",
        "//apps/crm/web/api-client:generate_client",
    ],
    tags = [
        "deploy",
        "dev",
        "migration",
        "terraform",
    ],
)

# Deploy existing dist to dev environment (no rebuild)
sh_binary(
    name = "deploy_only_web_dev",
    srcs = ["scripts/deploy_web_dev.sh"],
    tags = [
        "deploy",
        "dev",
        "terraform",
    ],
)

# Full deployment: database migration + web deployment
sh_binary(
    name = "deploy_full_dev",
    srcs = ["scripts/deploy_full_dev.sh"],
    data = [
        "//apps/crm/web:build_dist_complete",
        "//infra/database:migrate_gcp_dev_simple",
    ],
    tags = [
        "deploy",
        "dev",
        "full",
        "terraform",
    ],
)

# Infrastructure deployment: terraform apply + migration + web deployment
sh_binary(
    name = "deploy_infrastructure_dev",
    srcs = ["scripts/deploy_infrastructure_dev.sh"],
    data = [
        ":deploy_only_web_dev",
        "//apps/crm/web:build_dist_complete",
        "//infra/database:migrate_gcp_dev_simple",
    ],
    tags = [
        "deploy",
        "dev",
        "infrastructure",
        "terraform",
    ],
)

# Database migration only for dev environment
sh_binary(
    name = "migrate_db_dev",
    srcs = ["scripts/migrate_db_dev.sh"],
    data = [
        "//infra/database:migrate_gcp_dev_simple",
    ],
    tags = [
        "database",
        "dev",
        "migrate",
        "terraform",
    ],
)

# Terraform deployment with automatic migration detection
sh_binary(
    name = "terraform_with_auto_migration",
    srcs = ["scripts/terraform_with_auto_migration.sh"],
    data = [
        ":deploy_web_dev",
        "//infra/database:auto_migrate",
        "//infra/database:migrate_gcp_dev_simple",
    ],
    tags = [
        "auto-migration",
        "deploy",
        "dev",
        "terraform",
    ],
)

# Build and push backend Docker images to dev environment
sh_binary(
    name = "build_and_push_backend_dev",
    srcs = ["scripts/build_and_push_backend_dev.sh"],
    data = [
        "//apps/crm/backend:crm_backend_image",
        "//apps/project-hub/backend:project_hub_backend_image",
        "//services/auth:auth_service_image",
        "//services/gateway:gateway_image",
    ],
    tags = [
        "backend",
        "dev",
        "docker",
        "terraform",
    ],
)

# Deploy platform services to dev compute instance
sh_binary(
    name = "deploy_to_instance_dev",
    srcs = ["scripts/deploy_to_instance_dev.sh"],
    tags = [
        "deploy",
        "dev",
        "instance",
        "terraform",
    ],
)

# Deploy platform services using docker-compose (modern approach)
sh_binary(
    name = "deploy_compose_dev",
    srcs = ["scripts/deploy_compose_dev.sh"],
    data = [
        "docker-compose.yml",
        "nginx.conf",
        "//apps/crm/backend:crm_backend_image",
        "//apps/project-hub/backend:project_hub_backend_image",
        "//services/auth:auth_service_image",
        "//services/gateway:gateway_image",
    ],
    tags = [
        "compose",
        "deploy",
        "dev",
        "terraform",
    ],
)

# Install Docker on VM (if not already installed)
sh_binary(
    name = "install_docker_dev",
    srcs = ["scripts/install_docker_dev.sh"],
    tags = [
        "dev",
        "docker",
        "install",
        "terraform",
    ],
)

# One-time VM setup: Clean containers and upload docker-compose files
sh_binary(
    name = "setup_vm_dev",
    srcs = ["scripts/setup_vm_dev.sh"],
    data = [
        "docker-compose.yml",
        "nginx.conf",
    ],
    tags = [
        "dev",
        "setup",
        "terraform",
        "vm",
    ],
)

# Update Docker images only: Build, push, and update containers
sh_binary(
    name = "update_images_dev",
    srcs = ["scripts/update_images_dev.sh"],
    data = [
        "//apps/crm/backend:crm_backend_image",
        "//apps/project-hub/backend:project_hub_backend_image",
        "//services/auth:auth_service_image",
        "//services/gateway:gateway_image",
    ],
    tags = [
        "dev",
        "images",
        "terraform",
        "update",
    ],
)

# Update docker-compose configuration files
sh_binary(
    name = "update_config_dev",
    srcs = ["scripts/update_config_dev.sh"],
    data = [
        "docker-compose.yml",
        "nginx.conf",
    ],
    tags = [
        "config",
        "dev",
        "terraform",
        "update",
    ],
)

# ============================================================================
# GENERIC DEPLOYMENT SCRIPTS (WORK WITH ANY ENVIRONMENT)
# ============================================================================

# Generic infrastructure deployment
sh_binary(
    name = "deploy_infrastructure",
    srcs = ["scripts/generic/deploy_infrastructure.sh"],
    data = [
        "scripts/common/environment.sh",
        "scripts/common/utils.sh",
    ],
    tags = [
        "deploy",
        "generic",
        "infrastructure",
        "terraform",
    ],
)

# Generic database migration
sh_binary(
    name = "migrate_database",
    srcs = ["scripts/generic/migrate_database.sh"],
    data = [
        "scripts/common/environment.sh",
        "scripts/common/utils.sh",
        "//infra/database:migrate_gcp_dev_simple",
        "//infra/database:migrate_gcp_staging_simple",
    ],
    tags = [
        "database",
        "generic",
        "migrate",
        "terraform",
    ],
)

# Generic web deployment
sh_binary(
    name = "deploy_web",
    srcs = ["scripts/generic/deploy_web.sh"],
    data = [
        "scripts/common/environment.sh",
        "scripts/common/utils.sh",
        "//apps/crm/web:build_dist_complete",
        "//apps/crm/web/api-client:generate_client",
    ],
    tags = [
        "deploy",
        "generic",
        "terraform",
        "web",
    ],
)

# Generic VM configuration
sh_binary(
    name = "configure_vm",
    srcs = ["scripts/generic/configure_vm.sh"],
    data = [
        "docker-compose.yml",
        "nginx.conf",
        "scripts/common/environment.sh",
        "scripts/common/utils.sh",
    ],
    tags = [
        "configure",
        "generic",
        "terraform",
        "vm",
    ],
)

# Generic Docker image build and push
sh_binary(
    name = "build_and_push_images",
    srcs = ["scripts/generic/build_and_push_images.sh"],
    data = [
        "scripts/common/environment.sh",
        "scripts/common/utils.sh",
        "//apps/crm/backend:crm_backend_image",
        "//apps/project-hub/backend:project_hub_backend_image",
        "//services/auth:auth_service_image",
        "//services/gateway:gateway_image",
    ],
    tags = [
        "build",
        "generic",
        "images",
        "push",
        "terraform",
    ],
)

# Generic full deployment pipeline
sh_binary(
    name = "deploy_full",
    srcs = ["scripts/generic/deploy_full.sh"],
    data = [
        "scripts/common/environment.sh",
        "scripts/common/utils.sh",
        ":build_and_push_images",
        ":configure_vm",
        ":deploy_infrastructure",
        ":deploy_web",
        ":migrate_database",
    ],
    tags = [
        "deploy",
        "full",
        "generic",
        "terraform",
    ],
)

# ============================================================================
# ENVIRONMENT-SPECIFIC CONVENIENCE WRAPPERS
# ============================================================================

# Dev environment wrappers
sh_binary(
    name = "deploy_infrastructure_dev_new",
    srcs = ["scripts/generic/deploy_infrastructure.sh"],
    args = ["dev"],
    data = [
        "scripts/common/environment.sh",
        "scripts/common/utils.sh",
    ],
    tags = [
        "deploy",
        "dev",
        "infrastructure",
        "terraform",
    ],
)

sh_binary(
    name = "migrate_database_dev_new",
    srcs = ["scripts/generic/migrate_database.sh"],
    args = ["dev"],
    data = [
        "scripts/common/environment.sh",
        "scripts/common/utils.sh",
        "//infra/database:migrate_gcp_dev_simple",
    ],
    tags = [
        "database",
        "dev",
        "migrate",
        "terraform",
    ],
)

sh_binary(
    name = "deploy_web_dev_new",
    srcs = ["scripts/generic/deploy_web.sh"],
    args = ["dev"],
    data = [
        "scripts/common/environment.sh",
        "scripts/common/utils.sh",
        "//apps/crm/web:build_dist_complete",
        "//apps/crm/web/api-client:generate_client",
    ],
    tags = [
        "deploy",
        "dev",
        "terraform",
        "web",
    ],
)

sh_binary(
    name = "configure_vm_dev_new",
    srcs = ["scripts/generic/configure_vm.sh"],
    args = ["dev"],
    data = [
        "docker-compose.yml",
        "nginx.conf",
        "scripts/common/environment.sh",
        "scripts/common/utils.sh",
    ],
    tags = [
        "configure",
        "dev",
        "terraform",
        "vm",
    ],
)

sh_binary(
    name = "build_and_push_images_dev_new",
    srcs = ["scripts/generic/build_and_push_images.sh"],
    args = ["dev"],
    data = [
        "scripts/common/environment.sh",
        "scripts/common/utils.sh",
        "//apps/crm/backend:crm_backend_image",
        "//apps/project-hub/backend:project_hub_backend_image",
        "//services/auth:auth_service_image",
        "//services/gateway:gateway_image",
    ],
    tags = [
        "build",
        "dev",
        "images",
        "push",
        "terraform",
    ],
)

sh_binary(
    name = "deploy_full_dev_new",
    srcs = ["scripts/generic/deploy_full.sh"],
    args = ["dev"],
    data = [
        "scripts/common/environment.sh",
        "scripts/common/utils.sh",
        ":build_and_push_images",
        ":configure_vm",
        ":deploy_infrastructure",
        ":deploy_web",
        ":migrate_database",
    ],
    tags = [
        "deploy",
        "dev",
        "full",
        "terraform",
    ],
)

# Production environment wrappers
sh_binary(
    name = "deploy_infrastructure_prod",
    srcs = ["scripts/generic/deploy_infrastructure.sh"],
    args = ["prod"],
    data = [
        "scripts/common/environment.sh",
        "scripts/common/utils.sh",
    ],
    tags = [
        "deploy",
        "infrastructure",
        "prod",
        "terraform",
    ],
)

sh_binary(
    name = "migrate_database_prod",
    srcs = ["scripts/generic/migrate_database.sh"],
    args = ["prod"],
    data = [
        "scripts/common/environment.sh",
        "scripts/common/utils.sh",
        "//infra/database:migrate_gcp_dev_simple",
    ],
    tags = [
        "database",
        "migrate",
        "prod",
        "terraform",
    ],
)

sh_binary(
    name = "deploy_web_prod",
    srcs = ["scripts/generic/deploy_web.sh"],
    args = ["prod"],
    data = [
        "scripts/common/environment.sh",
        "scripts/common/utils.sh",
        "//apps/crm/web:build_dist_complete",
        "//apps/crm/web/api-client:generate_client",
    ],
    tags = [
        "deploy",
        "prod",
        "terraform",
        "web",
    ],
)

sh_binary(
    name = "configure_vm_prod",
    srcs = ["scripts/generic/configure_vm.sh"],
    args = ["prod"],
    data = [
        "docker-compose.yml",
        "nginx.conf",
        "scripts/common/environment.sh",
        "scripts/common/utils.sh",
    ],
    tags = [
        "configure",
        "prod",
        "terraform",
        "vm",
    ],
)

sh_binary(
    name = "build_and_push_images_prod",
    srcs = ["scripts/generic/build_and_push_images.sh"],
    args = ["prod"],
    data = [
        "scripts/common/environment.sh",
        "scripts/common/utils.sh",
        "//apps/crm/backend:crm_backend_image",
        "//apps/project-hub/backend:project_hub_backend_image",
        "//services/auth:auth_service_image",
        "//services/gateway:gateway_image",
    ],
    tags = [
        "build",
        "images",
        "prod",
        "push",
        "terraform",
    ],
)

sh_binary(
    name = "deploy_full_prod",
    srcs = ["scripts/generic/deploy_full.sh"],
    args = ["prod"],
    data = [
        "scripts/common/environment.sh",
        "scripts/common/utils.sh",
        ":build_and_push_images",
        ":configure_vm",
        ":deploy_infrastructure",
        ":deploy_web",
        ":migrate_database",
    ],
    tags = [
        "deploy",
        "full",
        "prod",
        "terraform",
    ],
)

# Staging environment wrappers
sh_binary(
    name = "deploy_infrastructure_staging",
    srcs = ["scripts/generic/deploy_infrastructure.sh"],
    args = ["staging"],
    data = [
        "scripts/common/environment.sh",
        "scripts/common/utils.sh",
    ],
    tags = [
        "deploy",
        "infrastructure",
        "staging",
        "terraform",
    ],
)

sh_binary(
    name = "migrate_database_staging",
    srcs = ["scripts/generic/migrate_database.sh"],
    args = ["staging"],
    data = [
        "scripts/common/environment.sh",
        "scripts/common/utils.sh",
        "//infra/database:migrate_gcp_staging_simple",
    ],
    tags = [
        "database",
        "migrate",
        "staging",
        "terraform",
    ],
)

sh_binary(
    name = "deploy_web_staging",
    srcs = ["scripts/generic/deploy_web.sh"],
    args = ["staging"],
    data = [
        "scripts/common/environment.sh",
        "scripts/common/utils.sh",
        "//apps/crm/web:build_dist_complete",
        "//apps/crm/web/api-client:generate_client",
    ],
    tags = [
        "deploy",
        "staging",
        "terraform",
        "web",
    ],
)

sh_binary(
    name = "configure_vm_staging",
    srcs = ["scripts/generic/configure_vm.sh"],
    args = ["staging"],
    data = [
        "docker-compose.yml",
        "nginx.conf",
        "scripts/common/environment.sh",
        "scripts/common/utils.sh",
    ],
    tags = [
        "configure",
        "staging",
        "terraform",
        "vm",
    ],
)

sh_binary(
    name = "build_and_push_images_staging",
    srcs = ["scripts/generic/build_and_push_images.sh"],
    args = ["staging"],
    data = [
        "scripts/common/environment.sh",
        "scripts/common/utils.sh",
        "//apps/crm/backend:crm_backend_image",
        "//apps/project-hub/backend:project_hub_backend_image",
        "//services/auth:auth_service_image",
        "//services/gateway:gateway_image",
    ],
    tags = [
        "build",
        "images",
        "push",
        "staging",
        "terraform",
    ],
)

sh_binary(
    name = "deploy_full_staging",
    srcs = ["scripts/generic/deploy_full.sh"],
    args = ["staging"],
    data = [
        "scripts/common/environment.sh",
        "scripts/common/utils.sh",
        ":build_and_push_images",
        ":configure_vm",
        ":deploy_infrastructure",
        ":deploy_web",
        ":migrate_database",
    ],
    tags = [
        "deploy",
        "full",
        "staging",
        "terraform",
    ],
)
