load("@rules_oci//oci:defs.bzl", "oci_image_rule")

# Project Hub Backend Docker Image
genrule(
    name = "project_hub_backend_image",
    srcs = [
        "Dockerfile",
        "package.json",
        "//apps/project-hub/backend/src:all_sources",
        "//apps/project-hub/backend/dist:all_dist_files",
        "//apps/project-hub/backend/prisma:all_prisma_files",
    ],
    outs = ["project_hub_backend_image.tar"],
    cmd = """
        # Change to the backend directory
        cd apps/project-hub/backend
        
        # Build the Docker image
        docker build -t project-hub-backend:latest .
        
        # Save the image as a tar file
        docker save project-hub-backend:latest > $(location project_hub_backend_image.tar)
    """,
    visibility = ["//visibility:public"],
    tags = ["docker", "manual"],
)

# Source files
filegroup(
    name = "all_sources",
    srcs = glob([
        "src/**/*.ts",
        "src/**/*.js", 
        "scripts/**/*.js",
        "*.json",
        "*.md",
    ]),
    visibility = ["//visibility:public"],
)

# Dist files (compiled output)
filegroup(
    name = "all_dist_files", 
    srcs = glob([
        "dist/**/*",
    ]),
    visibility = ["//visibility:public"],
)

# Prisma files
filegroup(
    name = "all_prisma_files",
    srcs = glob([
        "prisma/**/*",
        "generated/**/*",
    ]),
    visibility = ["//visibility:public"],
)

# Binary target for running locally
sh_binary(
    name = "project_hub_backend",
    srcs = ["run.sh"],
    data = [
        ":all_sources",
        ":all_dist_files", 
        ":all_prisma_files",
    ],
    visibility = ["//visibility:public"],
)