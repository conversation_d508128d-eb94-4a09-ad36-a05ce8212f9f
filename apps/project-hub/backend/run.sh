#!/bin/bash

# Change to the backend directory
cd apps/project-hub/backend

# Set default environment variables
export PORT=${PORT:-8005}
export NODE_ENV=${NODE_ENV:-development}

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "Installing dependencies..."
    npm install
fi

# Check if dist directory exists
if [ ! -d "dist" ]; then
    echo "Building TypeScript..."
    npm run build
fi

# Start the server
echo "Starting Project Hub Backend on port $PORT..."
npm start