import React, { useState } from 'react';
import { useParams, Navigate, Link } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import { apiClient } from '@/lib/api-client';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import {
  FileText,
  Calendar,
  CheckSquare,
  Code,
  TestTube,
  BookOpen,
  Rocket,
  Activity,
  ArrowLeft,
  Settings,
  AlertCircle,
  LayoutDashboard
} from 'lucide-react';

// Import tab components
import { PRDRequirementsTab } from '@/components/prd/PRDRequirementsTab';
import { ProjectOverviewTab } from '@/components/sdlc/ProjectOverviewTab';

export const ProjectSDLC: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { isAuthenticated, hasPermission } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');

  // Allow guest access for SDLC (like backend guestByDefault)
  const allowGuestAccess = true;

  // Fetch project data using API client (same as ProjectDetail)
  const { data: project, isLoading, error } = useQuery({
    queryKey: ['project-sdlc', id],
    queryFn: async () => {
      if (!id) throw new Error('Project ID is required');
      
      const projectData = await apiClient.getProject(id);
      
      const result = {
        ...projectData,
        tasks: projectData.tasks || [],
        progress: 0 // Will be calculated from tasks
      };

      return result;
    },
    enabled: !!id && (isAuthenticated || allowGuestAccess)
  });

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading project...</p>
        </div>
      </div>
    );
  }

  if (error) {
    console.error('ProjectSDLC error:', error);
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 mb-4">
            <AlertCircle className="h-12 w-12 mx-auto mb-2" />
            <p>Failed to load project</p>
            <p className="text-sm text-gray-500 mt-2">Error: {error.message}</p>
          </div>
          <Button asChild>
            <Link to="/">Back to Projects</Link>
          </Button>
        </div>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-gray-600 mb-4">
            <AlertCircle className="h-12 w-12 mx-auto mb-2" />
            <p>Project not found</p>
          </div>
          <Button asChild>
            <Link to="/">Back to Projects</Link>
          </Button>
        </div>
      </div>
    );
  }

  // Allow guest access OR authenticated users
  if (!isAuthenticated && !allowGuestAccess) {
    return <Navigate to="/login" replace />;
  }

  if (isAuthenticated && !hasPermission('project:read')) {
    return <Navigate to="/" replace />;
  }

  const getHealthColor = (health: string) => {
    switch (health) {
      case 'green': return 'bg-green-500';
      case 'yellow': return 'bg-yellow-500';
      case 'red': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getPhaseProgress = (phase: string) => {
    const phases = ['requirements', 'planning', 'implementation', 'testing', 'deployment', 'monitoring'];
    const currentIndex = phases.indexOf(project?.current_phase || 'requirements');
    const phaseIndex = phases.indexOf(phase);

    if (phaseIndex < currentIndex) return 100;
    if (phaseIndex === currentIndex) return project?.completion_percentage || 0;
    return 0;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* SDLC Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-9">
            <TabsTrigger value="overview" className="flex flex-col items-center space-y-1 p-3">
              <LayoutDashboard className="h-4 w-4" />
              <span className="text-xs">Overview</span>
              <Progress value={project?.completion_percentage || 0} className="w-full h-1" />
            </TabsTrigger>
            <TabsTrigger value="requirements" className="flex flex-col items-center space-y-1 p-3">
              <FileText className="h-4 w-4" />
              <span className="text-xs">Requirements</span>
              <Progress value={getPhaseProgress('requirements')} className="w-full h-1" />
            </TabsTrigger>
            <TabsTrigger value="planning" className="flex flex-col items-center space-y-1 p-3">
              <Calendar className="h-4 w-4" />
              <span className="text-xs">Planning</span>
              <Progress value={getPhaseProgress('planning')} className="w-full h-1" />
            </TabsTrigger>
            <TabsTrigger value="tasks" className="flex flex-col items-center space-y-1 p-3">
              <CheckSquare className="h-4 w-4" />
              <span className="text-xs">Tasks</span>
              <Progress value={getPhaseProgress('implementation')} className="w-full h-1" />
            </TabsTrigger>
            <TabsTrigger value="implementation" className="flex flex-col items-center space-y-1 p-3">
              <Code className="h-4 w-4" />
              <span className="text-xs">Implementation</span>
              <Progress value={getPhaseProgress('implementation')} className="w-full h-1" />
            </TabsTrigger>
            <TabsTrigger value="testing" className="flex flex-col items-center space-y-1 p-3">
              <TestTube className="h-4 w-4" />
              <span className="text-xs">Testing</span>
              <Progress value={getPhaseProgress('testing')} className="w-full h-1" />
            </TabsTrigger>
            <TabsTrigger value="documentation" className="flex flex-col items-center space-y-1 p-3">
              <BookOpen className="h-4 w-4" />
              <span className="text-xs">Documentation</span>
              <Progress value={getPhaseProgress('documentation')} className="w-full h-1" />
            </TabsTrigger>
            <TabsTrigger value="deployment" className="flex flex-col items-center space-y-1 p-3">
              <Rocket className="h-4 w-4" />
              <span className="text-xs">Deployment</span>
              <Progress value={getPhaseProgress('deployment')} className="w-full h-1" />
            </TabsTrigger>
            <TabsTrigger value="monitoring" className="flex flex-col items-center space-y-1 p-3">
              <Activity className="h-4 w-4" />
              <span className="text-xs">Monitoring</span>
              <Progress value={getPhaseProgress('monitoring')} className="w-full h-1" />
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview">
            <ProjectOverviewTab 
              project={project}
              projectId={id!}
              onProjectUpdate={() => {
                // Refetch project data when updated from overview tab
                window.location.reload(); // Simple approach for now
              }}
            />
          </TabsContent>

          <TabsContent value="requirements">
            <PRDRequirementsTab projectId={project?.id || id!} />
          </TabsContent>

          <TabsContent value="planning">
            <Card>
              <CardHeader>
                <CardTitle>Project Planning</CardTitle>
                <CardDescription>Plan project phases, milestones, and timelines</CardDescription>
              </CardHeader>
              <CardContent>
                <p>Project planning interface will be implemented here.</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="tasks">
            <Card>
              <CardHeader>
                <CardTitle>Task Management</CardTitle>
                <CardDescription>Manage project tasks and assignments</CardDescription>
              </CardHeader>
              <CardContent>
                <p>Enhanced task management interface will be implemented here.</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="implementation">
            <Card>
              <CardHeader>
                <CardTitle>Implementation Tracking</CardTitle>
                <CardDescription>Track development progress and code commits</CardDescription>
              </CardHeader>
              <CardContent>
                <p>Implementation tracking interface will be implemented here.</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="testing">
            <Card>
              <CardHeader>
                <CardTitle>Testing Management</CardTitle>
                <CardDescription>Manage test cases and quality assurance</CardDescription>
              </CardHeader>
              <CardContent>
                <p>Testing management interface will be implemented here.</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="documentation">
            <Card>
              <CardHeader>
                <CardTitle>Documentation</CardTitle>
                <CardDescription>Manage project documentation and guides</CardDescription>
              </CardHeader>
              <CardContent>
                <p>Documentation management interface will be implemented here.</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="deployment">
            <Card>
              <CardHeader>
                <CardTitle>Deployment Management</CardTitle>
                <CardDescription>Manage deployments and environments</CardDescription>
              </CardHeader>
              <CardContent>
                <p>Deployment management interface will be implemented here.</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="monitoring">
            <Card>
              <CardHeader>
                <CardTitle>Project Monitoring</CardTitle>
                <CardDescription>Monitor project health and performance</CardDescription>
              </CardHeader>
              <CardContent>
                <p>Project monitoring interface will be implemented here.</p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};
