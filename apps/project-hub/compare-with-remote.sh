#!/bin/bash

echo "=== COMPARING CURRENT WORK WITH REMOTE REPOSITORY ==="
echo ""

# Create temp directory
TEMP_DIR="/tmp/orbit-comparison"
CURRENT_DIR="/Users/<USER>/workspaces/git/orbit"

echo "📁 1. Setting up comparison environment..."
rm -rf $TEMP_DIR
mkdir -p $TEMP_DIR

echo "📥 2. Cloning fresh copy from remote..."
cd $TEMP_DIR
<NAME_EMAIL>:TwoDotAi/orbit.git remote-orbit
if [ $? -ne 0 ]; then
    echo "❌ Git clone failed. Trying with HTTPS..."
    git clone https://github.com/TwoDotAi/orbit.git remote-orbit
fi

echo "📋 3. Copying current working directory..."
cp -r "$CURRENT_DIR" "$TEMP_DIR/current-orbit"

echo "🔍 4. Comparing directories..."
cd "$TEMP_DIR"

echo ""
echo "=== FINDING CHANGED FILES ==="

# Compare the two directories
echo "📊 Files that exist in current but not in remote:"
diff -r remote-orbit current-orbit --brief | grep "Only in current-orbit" | head -20

echo ""
echo "📊 Files that are different between current and remote:"
diff -r remote-orbit current-orbit --brief | grep "differ" | head -20

echo ""
echo "🔍 5. Using git to see exact differences..."
cd "$TEMP_DIR/remote-orbit"

# Copy current files over remote files
echo "📁 Overwriting remote copy with current files..."
rsync -av --delete "$TEMP_DIR/current-orbit/" "$TEMP_DIR/remote-orbit/"

echo ""
echo "🎯 GIT STATUS AFTER COPYING CURRENT FILES:"
git status

echo ""
echo "🎯 MODIFIED FILES:"
git status --porcelain

echo ""
echo "🎯 LIST OF CHANGED FILES:"
git diff --name-only

echo ""
echo "🎯 DETAILED CHANGES (first 50 lines):"
git diff --stat | head -50

echo ""
echo "=== SUMMARY ==="
echo "✅ All modified files are listed above"
echo "✅ You can now see exactly what changed"
echo ""
echo "🚀 TO COMMIT AND PUSH THESE CHANGES:"
echo "cd $CURRENT_DIR"
echo "git add ."
echo "git commit -m 'Your commit message'"
echo "git push origin main"
echo ""
echo "🗑️ TO CLEANUP:"
echo "rm -rf $TEMP_DIR"