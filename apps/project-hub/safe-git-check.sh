#!/bin/bash

echo "=== SAFE GIT CHANGE DETECTION ==="
echo "Run this from: /Users/<USER>/workspaces/git/orbit"
echo ""

echo "🔍 1. Current git status (should show clean):"
git status --porcelain

echo ""
echo "🔍 2. Files modified today (based on timestamp):"
find apps/project-hub -type f \( -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" -o -name "*.env*" \) -newermt "2024-07-20" | head -10

echo ""
echo "🔍 3. Check if files have uncommitted changes:"
# Check specific files we know we modified
FILES_TO_CHECK=(
    "apps/project-hub/backend/.env"
    "apps/project-hub/backend/src/server.ts"
    "apps/project-hub/backend/src/services/aiAnalysisService.ts"
    "apps/project-hub/frontend/src/App.tsx"
    "apps/project-hub/frontend/src/pages/ProjectDetail.tsx"
    "apps/project-hub/frontend/src/pages/ProjectSDLC.tsx"
    "apps/project-hub/frontend/src/components/sdlc/ProjectOverviewTab.tsx"
    "apps/project-hub/frontend/src/lib/api-client.ts"
)

for file in "${FILES_TO_CHECK[@]}"; do
    if [ -f "$file" ]; then
        echo "   Checking: $file"
        git diff --quiet "$file" 2>/dev/null
        if [ $? -ne 0 ]; then
            echo "      ✅ HAS CHANGES"
        else
            echo "      ⚪ No changes"
        fi
    else
        echo "   ❌ File not found: $file"
    fi
done

echo ""
echo "🔍 4. Check for staged changes:"
git diff --cached --name-only

echo ""
echo "🔍 5. Recent commits with their files:"
git log --name-only --oneline -2

echo ""
echo "🔍 6. Simple way to add specific files:"
echo "If any files above show 'HAS CHANGES', add them individually:"
for file in "${FILES_TO_CHECK[@]}"; do
    echo "git add $file"
done

echo ""
echo "Then commit and push:"
echo "git commit -m 'Project Hub updates: Supabase→Prisma migration, Gemini AI, unified interface'"
echo "git push origin main"