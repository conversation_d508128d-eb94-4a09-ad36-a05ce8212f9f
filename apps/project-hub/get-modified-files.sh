#!/bin/bash

echo "=== EXTRACTING MODIFIED FILES FROM COMPARISON ==="
echo "Run this from: /Users/<USER>/workspaces/git/orbit"
echo ""

# Go to the comparison directory and extract the changed files
cd /tmp/orbit-comparison/remote-orbit 2>/dev/null

if [ $? -eq 0 ]; then
    echo "🎯 MODIFIED FILES (ready for git add):"
    echo ""
    
    # Get all modified files and filter for our project-hub changes
    git status --porcelain | grep -E "(apps/project-hub|^M |^A |^D )" | head -30
    
    echo ""
    echo "🎯 ALL MODIFIED FILES:"
    git diff --name-only | head -20
    
    echo ""
    echo "🎯 COMPLETE GIT STATUS:"
    git status --porcelain | head -20
    
    echo ""
    echo "=== COMMIT THESE CHANGES ==="
    echo "From /Users/<USER>/workspaces/git/orbit directory:"
    echo ""
    echo "# Add the modified files shown above:"
    git status --porcelain | grep -E "apps/project-hub" | awk '{print "git add " $2}' | head -10
    echo ""
    echo "# Then commit:"
    echo 'git commit -m "Complete Project Hub modernization: Supabase→Prisma + Gemini AI + Unified Interface"'
    echo ""
    echo "# Then push:"
    echo "git push origin main"
else
    echo "❌ Comparison directory not found. Run the compare-with-remote.sh script first."
fi