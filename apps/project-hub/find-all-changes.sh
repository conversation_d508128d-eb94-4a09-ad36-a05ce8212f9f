#!/bin/bash

echo "=== FINDING ALL MODIFIED FILES IN ORBIT REPOSITORY ==="
echo "Run this script from: /Users/<USER>/workspaces/git/orbit"
echo ""

echo "1. Files modified but not staged:"
git diff --name-only

echo ""
echo "2. Files staged for commit:"
git diff --cached --name-only

echo ""
echo "3. Untracked files (not ignored):"
git ls-files --others --exclude-standard

echo ""
echo "4. All files with any changes (including untracked):"
git status --porcelain

echo ""
echo "5. Recent commits (last 5):"
git log --oneline -5

echo ""
echo "6. Files changed since last commit:"
git diff HEAD --name-only

echo ""
echo "7. Files in working directory vs index:"
git diff-index --name-only HEAD

echo ""
echo "8. All modified files in project-hub specifically:"
find apps/project-hub -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" -o -name "*.env*" -o -name "*.json" -o -name "*.prisma" | xargs git status --porcelain 2>/dev/null

echo ""
echo "9. Files modified in last 24 hours in project-hub:"
find apps/project-hub -type f \( -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" -o -name "*.env*" -o -name "*.json" -o -name "*.prisma" \) -mtime -1

echo ""
echo "=== SUMMARY ==="
echo "Copy the files listed above and use:"
echo "git add <filename>"
echo "git commit -m 'Your commit message'"
echo "git push origin main"