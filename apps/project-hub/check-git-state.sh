#!/bin/bash

echo "=== COMPREHENSIVE GIT STATE CHECK ==="
echo "Run this from: /Users/<USER>/workspaces/git/orbit"
echo ""

echo "🔍 1. CURRENT BRANCH AND TRACKING:"
git branch -vv
echo ""

echo "🔍 2. REMOTE REPOSITORIES:"
git remote -v
echo ""

echo "🔍 3. FETCH LATEST FROM REMOTE:"
git fetch origin
echo ""

echo "🔍 4. UNPUSHED COMMITS (Local ahead of remote):"
git log --oneline origin/main..HEAD
if [ $? -ne 0 ]; then
    echo "   (No unpushed commits or no remote tracking)"
fi
echo ""

echo "🔍 5. COMMITS BEHIND REMOTE (Remote ahead of local):"
git log --oneline HEAD..origin/main
if [ $? -ne 0 ]; then
    echo "   (Local is up to date with remote)"
fi
echo ""

echo "🔍 6. LAST 10 LOCAL COMMITS:"
git log --oneline -10
echo ""

echo "🔍 7. CURRENT WORKING DIRECTORY STATUS:"
git status
echo ""

echo "🔍 8. UNCOMMITTED CHANGES:"
echo "   Modified files:"
git diff --name-only
echo "   Staged files:"
git diff --cached --name-only
echo "   Untracked files:"
git ls-files --others --exclude-standard
echo ""

echo "🔍 9. RECENT COMMITS WITH FILE CHANGES:"
git log --name-only --oneline -3
echo ""

echo "🔍 10. FILES CHANGED IN PROJECT-HUB (Last 24 hours):"
find apps/project-hub -type f \( -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" -o -name "*.env*" -o -name "*.json" -o -name "*.prisma" \) -mtime -1 2>/dev/null
echo ""

echo "🔍 11. GIT LOG WITH DATES (Last 5 commits):"
git log --oneline --date=short --pretty=format:"%h %ad %s" -5
echo ""
echo ""

echo "=== SUMMARY ==="
echo "✅ If section 4 shows commits: You have unpushed commits → run 'git push origin main'"
echo "✅ If section 8 shows files: You have uncommitted changes → add, commit, then push"
echo "✅ If everything is clean: All changes are already pushed to remote"
echo ""
echo "🚀 TO PUSH (if needed):"
echo "   git push origin main"